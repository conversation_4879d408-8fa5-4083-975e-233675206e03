# ML Log Prediction - Advanced Machine Learning Pipeline for Well Log Data

A comprehensive machine learning pipeline for predicting and imputing missing well log data using multiple advanced algorithms including gradient boosting models, deep learning approaches, and traditional statistical methods. This project features GPU acceleration, advanced preprocessing, and comprehensive visualization capabilities.

## 🎯 Project Overview

This project provides a complete workflow for:
- **Loading and processing LAS (Log ASCII Standard) files**
- **Advanced data cleaning and quality control**
- **Multi-model machine learning prediction and imputation**
- **Comprehensive performance evaluation and visualization**
- **GPU-accelerated training for improved performance**
- **Professional reporting and output generation**

## 🏗️ Architecture

### Core Components
- **`main.py`**: Entry point with interactive GUI workflow orchestrating the complete ML pipeline
- **`data_handler.py`**: LAS file operations, data loading, cleaning, and preprocessing
- **`ml_core.py`**: Machine learning model registry and training pipeline with MODEL_REGISTRY
- **`config_handler.py`**: User interfaces, file selection dialogs, and configuration management
- **`reporting.py`**: Visualization, analysis, and performance reporting

### Model Registry Architecture
The `MODEL_REGISTRY` in `ml_core.py` contains all available models with proper configuration including:
- **type**: 'shallow' (gradient boosting, statistical), 'deep' (basic neural networks), or 'deep_advanced' (SAITS, BRITS, etc.)
- **model_class**: Reference to the model implementation class
- **requires_sequences**: Boolean indicating if the model needs sequence data
- **config**: Model-specific hyperparameters and settings

Example registry entry:
```python
'xgboost': {
    'type': 'shallow',
    'model_class': XGBRegressor,
    'requires_sequences': False,
    'config': {'device': 'cuda', 'tree_method': 'gpu_hist'}
}
```

### Model Categories

#### Gradient Boosting Models (GPU-Accelerated)
- **XGBoost**: Modern GPU acceleration with `device='cuda'`
- **LightGBM**: High-performance gradient boosting
- **CatBoost**: Categorical feature handling with GPU support

#### Deep Learning Models
- **Autoencoder**: Neural network-based imputation
- **U-Net**: Advanced sequence-to-sequence learning  
- **Advanced Models**: SAITS, BRITS, Transformer, MRNN (from PyPOTS library)

#### Statistical Models
- **Linear Regression**: Interpretable baseline with diagnostics
- **Ridge Regression**: L2 regularization for multicollinearity

## 🚀 Key Features

### Advanced Data Processing
- **Automated LAS file loading** with error handling
- **Smart data cleaning** with domain-specific rules
- **Enhanced preprocessing** with outlier detection
- **Sequence creation** for deep learning models
- **Data leakage detection** for model validation

### GPU Acceleration & Memory Optimization (Phase 1)
- **XGBoost GPU optimization** with modern CUDA support
- **Automatic fallback** to CPU when GPU unavailable
- **Environment Configuration**: `PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True` set before PyTorch imports
- **Memory Monitoring**: Real-time memory usage tracking via `utils.memory_optimization`
- **Mixed Precision Training**: Automatic AMP (Automatic Mixed Precision) for deep learning models
- **GPU Fallback**: Graceful fallback to CPU when GPU memory is insufficient
- **Performance monitoring** and benchmarking

### Multi-Model Comparison
- **Batch model execution** with comprehensive comparison
- **Automated model ranking** based on composite scores
- **Side-by-side visualization** of model performance
- **Statistical evaluation** with multiple metrics (MAE, R², RMSE)

### Professional Visualization
- **Quality control plots** with cross-plot analysis
- **Model performance dashboards**
- **Residual analysis and diagnostics**
- **Publication-ready charts** with customizable styling

## 📋 Requirements

### System Requirements
- **Python 3.8+**
- **NVIDIA GPU** (optional, for GPU acceleration)
- **CUDA Toolkit** (for GPU support)

### Dependencies
See `requirements.txt` for complete list:
```
# Core Data Science
pandas>=1.3.0
numpy>=1.21.0
scikit-learn>=1.0.0

# Machine Learning
xgboost>=1.5.0
lightgbm>=3.2.0
catboost>=1.0.0

# Deep Learning
torch>=1.9.0
pypots>=0.2.0
monai>=0.9.0

# Well Log Processing
lasio>=0.30

# Visualization
matplotlib>=3.5.0
plotly>=5.0.0
seaborn>=0.11.0

# Optimization
optuna>=3.0.0
```

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd ML_Log_Prediction
   ```

2. **Create virtual environment**:
   ```bash
   # Create environment
   python -m venv mwlt
   
   # Activate environment
   # Windows:
   mwlt\Scripts\activate
   # Linux/Mac:
   source mwlt/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## 🧪 Testing and Code Quality

### Code Quality
```bash
# Linting (flake8 is available)
python -m flake8 main.py ml_core.py data_handler.py

# No pytest framework is currently installed
```

### Testing Individual Components
```bash
# Test simple functionality
python archives/debug/general_testing/simple_test.py

# Test specific model implementations  
python tests/test_file.py

# Test Phase 1 minimal functionality
python tests/test_phase1_minimal.py

# Test memory optimization
python tests/test_memory_optimization.py
```

## 🎮 Usage

### Quick Start
```bash
python main.py
```

The application provides an interactive GUI workflow:

1. **Select LAS files** using file dialog
2. **Configure feature and target logs**
3. **Choose training/prediction strategy**
4. **Select models** for comparison
5. **Review results** and generate reports

### Example Workflow
```python
# Load and process data
df, las_objs, wells, logs = load_las_files_from_directory(input_path)
clean_df = clean_log_data(df)

# Configure models
selected_models = ['xgboost', 'lightgbm', 'autoencoder']

# Run multi-model comparison
results = run_multiple_models(clean_df, features, target, selected_models)

# Generate comprehensive analysis
create_multi_model_comparison_plots(results)
generate_final_report(results)
```

## 📊 Model Performance

### Current Environment
- **GPU**: NVIDIA GeForce RTX 2070 Super (8GB VRAM)
- **CUDA**: Version 12.1
- **Python**: 3.10
- **Virtual Environment**: `mwlt`

### Supported Metrics
- **Mean Absolute Error (MAE)**
- **R-squared (R²)**
- **Root Mean Square Error (RMSE)**
- **Composite Score** (weighted combination)

## 📁 Pipeline Files and Dependencies

This section provides a comprehensive overview of all files involved in the main pipeline execution when running `main.py`. Understanding these dependencies is crucial for maintaining, debugging, and extending the ML log prediction system.

### 🔧 Core Pipeline Files

These are the essential files that form the backbone of the pipeline execution:

#### **`main.py`** - Main Entry Point
- **Type**: Source code file (Python script)
- **Purpose**: Orchestrates the complete ML workflow from data loading to result generation
- **Role**: Entry point that coordinates all pipeline steps including file selection, data processing, model execution, and output generation
- **Dependencies**: Imports from all other core modules
- **Critical for**: Pipeline execution, user interaction, workflow management

#### **`data_handler.py`** - Data Processing Engine
- **Type**: Source code file (Python module)
- **Purpose**: Handles all LAS file operations, data loading, cleaning, and preprocessing
- **Role**: Core data management including file I/O, data validation, normalization, and sequence creation
- **Dependencies**: `lasio`, `pandas`, `numpy`, `sklearn`, optional `enhanced_preprocessing`
- **Critical for**: Data loading, preprocessing, result writing to LAS files

#### **`ml_core.py`** - Machine Learning Engine
- **Type**: Source code file (Python module)
- **Purpose**: Contains the model registry and implements all ML prediction pipelines
- **Role**: Manages model selection, training, evaluation, and prediction workflows
- **Dependencies**: ML libraries (`xgboost`, `lightgbm`, `catboost`, `torch`), model implementations, utility modules
- **Critical for**: Model execution, performance evaluation, prediction generation

#### **`config_handler.py`** - Configuration Manager
- **Type**: Source code file (Python module)
- **Purpose**: Manages user interfaces, file selection dialogs, and configuration settings
- **Role**: Handles user input, file/directory selection, parameter configuration
- **Dependencies**: `tkinter` for GUI dialogs, imports `MODEL_REGISTRY` from `ml_core`
- **Critical for**: User interaction, parameter configuration, file selection

#### **`reporting.py`** - Visualization and Analysis
- **Type**: Source code file (Python module)
- **Purpose**: Generates all visualizations, quality control reports, and performance analysis
- **Role**: Creates plots, charts, statistical reports, and model comparison visualizations
- **Dependencies**: `matplotlib`, `pandas`, `numpy`, `sklearn.metrics`, `utils.display_utils`
- **Critical for**: Result visualization, quality control, performance reporting

### 🧩 Model Implementation Files

#### **`models/`** Directory
- **Type**: Source code directory
- **Purpose**: Contains all machine learning model implementations

##### **`models/__init__.py`**
- **Type**: Python package initializer
- **Purpose**: Makes the models directory a Python package
- **Role**: Enables importing model classes from the models directory

##### **`models/simple_autoencoder.py`**
- **Type**: Source code file (Model implementation)
- **Purpose**: Implements SimpleAutoencoder and SimpleUNet deep learning models
- **Role**: Provides neural network-based imputation models for the pipeline
- **Dependencies**: `torch`, `torch.nn`
- **Critical for**: Deep learning model execution

##### **`models/advanced_models/`** Directory
- **Type**: Source code directory
- **Purpose**: Contains advanced deep learning model implementations
- **Role**: Houses sophisticated models like SAITS, BRITS, Transformer, etc.
- **Files included**:
  - `__init__.py` - Package initializer with model registry
  - `base_model.py` - Base class for advanced models
  - `saits_model.py` - SAITS imputation model
  - `brits_model.py` - BRITS imputation model
  - `enhanced_unet.py` - Enhanced U-Net implementation
  - `transformer_model.py` - Transformer-based model
  - `mrnn_model.py` - Multi-directional RNN model
  - `utils/` - Utility functions for advanced models

### 🛠️ Utility Modules

#### **`utils/`** Directory
- **Type**: Source code directory
- **Purpose**: Contains specialized utility modules for enhanced functionality

##### **`utils/__init__.py`**
- **Type**: Python package initializer
- **Purpose**: Makes the utils directory a Python package

##### **`utils/display_utils.py`**
- **Type**: Source code file (Utility module)
- **Purpose**: Handles cross-platform display formatting, emoji support, and font management
- **Role**: Ensures consistent visualization across different operating systems
- **Dependencies**: Standard library modules
- **Critical for**: Report formatting, visualization consistency

##### **`utils/gpu_acceleration.py`**
- **Type**: Source code file (Utility module)
- **Purpose**: Manages GPU acceleration and CUDA operations
- **Role**: Optimizes model training with GPU support
- **Dependencies**: `torch`, CUDA libraries
- **Critical for**: GPU-accelerated model training

##### **`utils/optimization.py`**
- **Type**: Source code file (Utility module)
- **Purpose**: Provides optimization utilities and performance enhancements
- **Role**: Implements optimization strategies for model training
- **Dependencies**: Various ML libraries
- **Critical for**: Performance optimization

##### **`utils/xgboost_gpu_utils.py`**
- **Type**: Source code file (Utility module)
- **Purpose**: Specialized utilities for XGBoost GPU acceleration
- **Role**: Manages XGBoost-specific GPU configurations and optimizations
- **Dependencies**: `xgboost`, CUDA libraries
- **Critical for**: XGBoost GPU acceleration

##### **`utils/memory_optimization.py`**
- **Type**: Source code file (Utility module)
- **Purpose**: Memory management and optimization utilities
- **Role**: Monitors and optimizes memory usage during pipeline execution
- **Dependencies**: `psutil`, `memory-profiler`
- **Critical for**: Large dataset processing

##### **`utils/performance_monitor.py`**
- **Type**: Source code file (Utility module)
- **Purpose**: Performance monitoring and benchmarking utilities
- **Role**: Tracks execution time, resource usage, and performance metrics
- **Dependencies**: Standard library, monitoring tools
- **Critical for**: Performance analysis and optimization

##### **`utils/visualization_advanced.py`**
- **Type**: Source code file (Utility module)
- **Purpose**: Advanced visualization functions and plotting utilities
- **Role**: Provides sophisticated plotting capabilities beyond basic reporting
- **Dependencies**: `matplotlib`, `plotly`, `seaborn`
- **Critical for**: Advanced visualization features

### 📋 Configuration Files

#### **`requirements.txt`**
- **Type**: Configuration file (Package dependencies)
- **Purpose**: Specifies all Python package dependencies and versions
- **Role**: Ensures consistent environment setup across different installations
- **Critical for**: Environment setup, dependency management

#### **`config/display_config.ini`**
- **Type**: Configuration file (Display settings)
- **Purpose**: Contains display and visualization configuration settings
- **Role**: Manages cross-platform display compatibility and formatting preferences
- **Critical for**: Consistent visualization across different systems

### 🔧 Optional Enhancement Modules

These modules provide enhanced functionality but are not required for basic pipeline operation:

#### **`enhanced_preprocessing.py`**
- **Type**: Source code file (Optional enhancement)
- **Purpose**: Provides advanced preprocessing capabilities with improved algorithms
- **Role**: Offers enhanced data cleaning, normalization, and sequence creation
- **Dependencies**: Advanced preprocessing libraries
- **Critical for**: Enhanced data quality and preprocessing performance

#### **`mlr_utils.py`**
- **Type**: Source code file (Optional utility)
- **Purpose**: Multiple Linear Regression utilities and diagnostic functions
- **Role**: Provides interpretable linear models with statistical diagnostics
- **Dependencies**: `sklearn`, `statsmodels`
- **Critical for**: Interpretable model analysis and statistical validation

#### **`data_leakage_detector.py`**
- **Type**: Source code file (Optional utility)
- **Purpose**: Detects potential data leakage in machine learning models
- **Role**: Validates model integrity and prevents overfitting due to data leakage
- **Dependencies**: Statistical analysis libraries
- **Critical for**: Model validation and integrity checking

### 📊 Data Files

#### **`Las/`** Directory
- **Type**: Data directory
- **Purpose**: Contains input LAS (Log ASCII Standard) files for processing
- **Role**: Provides well log data for the machine learning pipeline
- **File types**: `.las` files containing well log measurements
- **Critical for**: Pipeline input data

### 📈 Output Directories

#### **`plots/`** Directory
- **Type**: Output directory
- **Purpose**: Stores generated visualization plots and charts
- **Role**: Contains all graphical outputs from the reporting module
- **File types**: `.png`, `.jpg` image files
- **Critical for**: Visualization output storage

#### **`catboost_info/`** Directory
- **Type**: Output directory (Auto-generated)
- **Purpose**: Contains CatBoost model training information and logs
- **Role**: Stores CatBoost-specific training metadata and performance logs
- **File types**: `.json`, `.tsv` files
- **Critical for**: CatBoost model debugging and analysis

### 🔄 Execution Flow Dependencies

When `main.py` is executed, the following dependency chain is activated:

```
main.py
├── config_handler.py (UI, file selection, configuration)
├── data_handler.py (LAS loading, cleaning, preprocessing)
├── ml_core.py (model registry, training pipeline)
│   ├── models/ (all model implementations)
│   │   ├── simple_autoencoder.py
│   │   ├── advanced_models/ (SAITS, BRITS, Transformer, etc.)
│   │   └── __init__.py
│   └── utils/ (GPU, memory, performance utilities)
└── reporting.py (visualization, analysis, output)
```

**Optional enhancement modules:**
- `ml_core_phase1_integration.py`: Enhanced preprocessing for deep learning
- `mlr_utils.py`: Multiple Linear Regression with diagnostics 
- `data_leakage_detector.py`: Data validation utilities

**Workflow Steps:**
1. **Initialization**: `main.py` imports core modules
2. **Configuration**: `config_handler.py` manages user input and file selection
3. **Data Loading**: `data_handler.py` loads and preprocesses LAS files
4. **Model Execution**: `ml_core.py` coordinates model training and prediction
5. **Visualization**: `reporting.py` generates plots and analysis
6. **Utilities**: Various `utils/` modules provide specialized functionality
7. **Models**: `models/` directory provides model implementations
8. **Output**: Results are saved to designated output directories

### ⚠️ Critical Dependencies

For the pipeline to function properly, these files must be present and accessible:

- **Essential**: `main.py`, `data_handler.py`, `ml_core.py`, `config_handler.py`, `reporting.py`
- **Required**: `requirements.txt`, `models/__init__.py`, `utils/__init__.py`
- **Model-dependent**: `models/simple_autoencoder.py` (for deep learning models)
- **GPU-dependent**: `utils/gpu_acceleration.py`, `utils/xgboost_gpu_utils.py` (for GPU acceleration)
- **Input-dependent**: LAS files in `Las/` directory or user-selected files

### 🚀 Getting Started with Pipeline Files

To run the complete pipeline:

1. **Ensure all core files are present** in the project root
2. **Install dependencies** from `requirements.txt`
3. **Prepare input data** (LAS files)
4. **Run the main script**: `python main.py`
5. **Follow interactive prompts** for configuration
6. **Review outputs** in designated directories

## 🔧 Configuration

### GPU Configuration
GPU acceleration is automatically detected and configured:
- **XGBoost**: Uses `device='cuda'` for modern GPU support
- **LightGBM**: Configures `device='gpu'` when available
- **CatBoost**: Sets `task_type='GPU'` for CUDA acceleration

### Model Hyperparameters
All models support customizable hyperparameters with:
- **Default values** optimized for well log data
- **Interactive configuration** during runtime
- **Automated tuning** with Optuna (optional)

## 📈 Performance Optimization

### Phase Implementation Status
- **Phase 1**: Memory optimization and basic model stability ✅ 
- **Phase 2**: Advanced deep learning models (SAITS, BRITS, Transformer) - In Progress
- **Phase 3**: Enhanced preprocessing and validation - Planned
- **Phase 4**: Performance optimization and deployment - Planned

### Optimization Features
- **Memory profiling** with automatic optimization
- **Batch processing** for large datasets
- **Sequence optimization** for deep learning models
- **GPU memory management** with fallback strategies

## 🤝 Contributing

Contributions are welcome! Please see our development guidelines:
1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📝 License

[Specify your license here]

## 📞 Support

For questions, issues, or feature requests:
- Create an issue in the repository
- Check the documentation in `/docs`
- Review example notebooks in `/example`

## 🙏 Acknowledgments

- **PyPOTS** for advanced time series imputation models
- **XGBoost, LightGBM, CatBoost** teams for excellent gradient boosting implementations
- **PyTorch** and **MONAI** for deep learning capabilities
- **Lasio** for LAS file processing support
