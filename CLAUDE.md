# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive machine learning pipeline for predicting and imputing missing well log data using advanced algorithms including gradient boosting models, deep learning approaches, and traditional statistical methods. The project features GPU acceleration, advanced preprocessing, and comprehensive visualization capabilities.

## Environment Setup

### Virtual Environment
The project uses a virtual environment called `mwlt`. Create and activate it before running any commands:
```bash
# Create environment
python -m venv mwlt

# Activate environment
# Windows:
mwlt\Scripts\activate
# Linux/Mac:
source mwlt/bin/activate
```

### Dependencies
Install required packages from requirements.txt:
```bash
pip install -r requirements.txt
```

### GPU Support
The project supports GPU acceleration with CUDA. Key GPU-accelerated components:
- XGBoost with `device='cuda'`
- PyTorch models for deep learning
- CatBoost with `task_type='GPU'`

## Common Commands

### Running the Main Pipeline
```bash
python main.py
```
This launches the interactive GUI workflow for the complete ML pipeline.

### Code Quality
```bash
# Linting (flake8 is available)
python -m flake8 main.py ml_core.py data_handler.py

# No pytest framework is currently installed
```

### Testing Individual Components
```bash
# Test simple functionality
python archives/debug/general_testing/simple_test.py

# Test specific model implementations  
python tests/test_file.py

# Test Phase 1 minimal functionality
python tests/test_phase1_minimal.py

# Test memory optimization
python tests/test_memory_optimization.py
```

## Architecture Overview

### Core Pipeline Files
- **`main.py`**: Entry point with interactive GUI workflow orchestrating the complete ML pipeline
- **`data_handler.py`**: LAS file operations, data loading, cleaning, and preprocessing
- **`ml_core.py`**: Machine learning model registry and training pipeline with MODEL_REGISTRY
- **`config_handler.py`**: User interfaces, file selection dialogs, and configuration management
- **`reporting.py`**: Visualization, analysis, and performance reporting

### Model Implementation Structure
- **`models/`**: Contains all ML model implementations
  - `simple_autoencoder.py`: Neural network-based imputation models
  - `advanced_models/`: SAITS, BRITS, Transformer, MRNN implementations
  - `__init__.py`: Package initialization with model registry

### Utility Modules
- **`utils/`**: Specialized functionality
  - `gpu_acceleration.py`: GPU optimization and CUDA operations  
  - `xgboost_gpu_utils.py`: XGBoost-specific GPU configurations
  - `display_utils.py`: Cross-platform display formatting
  - `memory_optimization.py`: Memory management utilities
  - `performance_monitor.py`: Performance tracking and benchmarking

### Data and Configuration
- **`Las/`**: Input LAS (Log ASCII Standard) well log files
- **`config/display_config.ini`**: Display and visualization settings
- **`plots/`**: Generated visualization outputs
- **`requirements.txt`**: Python package dependencies

## Model Categories

### Gradient Boosting Models (GPU-Accelerated)
- **XGBoost**: Modern GPU acceleration with `device='cuda'`
- **LightGBM**: High-performance gradient boosting with `device='gpu'`
- **CatBoost**: Categorical feature handling with `task_type='GPU'`

### Deep Learning Models  
- **Autoencoder**: Neural network-based imputation
- **U-Net**: Advanced sequence-to-sequence learning
- **Advanced Models**: SAITS, BRITS, Transformer, MRNN (from PyPOTS library)

### Statistical Models
- **Linear Regression**: Interpretable baseline with diagnostics
- **Ridge Regression**: L2 regularization for multicollinearity

## Workflow Steps

1. **File Selection**: GUI dialog for LAS file selection
2. **Data Loading**: Automated LAS file processing with error handling
3. **Log Configuration**: Feature and target log selection
4. **Training Strategy**: Well separation and prediction mode configuration
5. **Model Selection**: Multi-model selection from MODEL_REGISTRY
6. **Execution**: Batch model training and evaluation
7. **Analysis**: Performance comparison and ranking
8. **Visualization**: Comprehensive plotting and quality control
9. **Output**: Results export to LAS files and reports

## Key Features

### Multi-Model Comparison
- Batch execution of multiple models simultaneously
- Automated performance ranking based on composite scores
- Side-by-side visualization comparisons
- Statistical evaluation with MAE, R², RMSE metrics

### Advanced Data Processing
- Smart data cleaning with domain-specific rules for well log data
- Enhanced preprocessing with outlier detection
- Data leakage detection for model validation
- Sequence creation for deep learning models

### Professional Visualization
- Quality control plots with cross-plot analysis
- Model performance dashboards and residual analysis
- Publication-ready charts with customizable styling
- Multi-model comparison visualizations

## Development Notes

### Model Registry
The `MODEL_REGISTRY` in `ml_core.py` contains all available models. New models should be registered here with proper configuration including:
- **type**: 'shallow' (gradient boosting, statistical), 'deep' (basic neural networks), or 'deep_advanced' (SAITS, BRITS, etc.)
- **model_class**: Reference to the model implementation class
- **requires_sequences**: Boolean indicating if the model needs sequence data
- **config**: Model-specific hyperparameters and settings

Example registry entry:
```python
'xgboost': {
    'type': 'shallow',
    'model_class': XGBRegressor,
    'requires_sequences': False,
    'config': {'device': 'cuda', 'tree_method': 'gpu_hist'}
}
```

### GPU Memory Management
The codebase includes automatic GPU detection with CPU fallback strategies. Memory optimization utilities handle large datasets efficiently.

### File Structure Dependencies
Core pipeline execution requires all files in the dependency chain:
```
main.py
├── config_handler.py (UI, file selection, configuration)
├── data_handler.py (LAS loading, cleaning, preprocessing)
├── ml_core.py (model registry, training pipeline)
│   ├── models/ (all model implementations)
│   │   ├── simple_autoencoder.py
│   │   ├── advanced_models/ (SAITS, BRITS, Transformer, etc.)
│   │   └── __init__.py
│   └── utils/ (GPU, memory, performance utilities)
└── reporting.py (visualization, analysis, output)
```

Optional enhancement modules:
- `ml_core_phase1_integration.py`: Enhanced preprocessing for deep learning
- `mlr_utils.py`: Multiple Linear Regression with diagnostics 
- `data_leakage_detector.py`: Data validation utilities

### Interactive Workflow
The main pipeline provides extensive user interaction through console selections and GUI dialogs for file management, making it suitable for both technical users and domain experts.

### Memory Optimization (Phase 1)
The project includes Phase 1 memory optimization features:
- **Environment Configuration**: `PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True` set before PyTorch imports
- **Memory Monitoring**: Real-time memory usage tracking via `utils.memory_optimization`
- **Mixed Precision Training**: Automatic AMP (Automatic Mixed Precision) for deep learning models
- **GPU Fallback**: Graceful fallback to CPU when GPU memory is insufficient

### Phase Implementation Status
- **Phase 1**: Memory optimization and basic model stability ✅ 
- **Phase 2**: Advanced deep learning models (SAITS, BRITS, Transformer) - In Progress
- **Phase 3**: Enhanced preprocessing and validation - Planned
- **Phase 4**: Performance optimization and deployment - Planned